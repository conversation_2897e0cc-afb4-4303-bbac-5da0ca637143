"""
星座任务规划的训练脚本
"""
import os
import time
import datetime
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
from constellation_smp.constellation_smp import ConstellationSMPDataset, reward, render
from constellation_smp.model_factory import ModelFactory, ModelManager
from constellation_smp.config_manager import ConfigManager
from hyperparameter import args
from pict import plot_single_smp_train_loss

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# 全局日志文件对象
log_file = None

def log_message(message, save_to_file=True, to_console=True):
    """
    将消息同时输出到控制台和日志文件
    """
    if to_console:
        print(message)
    if save_to_file and log_file is not None:
        log_file.write(message + '\n')
        log_file.flush()


def validate_constellation_smp(data_loader, actor, reward_fn, num_satellites, render_fn=None, save_dir='.', num_plot=5, verbose=False, constellation_mode='cooperative'):
    """
    验证星座任务规划模型
    """
    actor.eval()
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    # 增强的性能指标跟踪
    total_reward = 0
    total_revenue_rate = 0
    total_distance = 0
    total_memory = 0
    total_power = 0
    total_samples = 0

    # 新增的详细指标
    total_tasks_completed = 0
    total_tasks_available = 0
    total_efficiency_score = 0
    reward_variance = []
    revenue_rate_variance = []

    # 负载均衡跟踪
    satellite_task_distribution = torch.zeros(num_satellites)
    load_balance_scores = []
    
    for batch_idx, batch in enumerate(data_loader):
        static, dynamic, x0 = batch
        static = static.to(device)
        dynamic = dynamic.to(device)
        batch_size = static.size(0)

        with torch.no_grad():
            tour_indices, satellite_indices, _ = actor.forward(static, dynamic)
        
        # 获取详细指标
        reward, revenue_rate, distance, memory, power = reward_fn(static, tour_indices, satellite_indices, constellation_mode)

        total_samples += batch_size
        total_reward += reward.sum().item()
        total_revenue_rate += revenue_rate.sum().item()
        total_distance += distance.sum().item()
        total_memory += memory.sum().item()
        total_power += power.sum().item()

        # 计算任务完成情况
        batch_tasks_completed = (tour_indices != 0).sum(dim=1).float()  # 排除起始节点
        batch_tasks_available = static.size(2)  # 总任务数
        total_tasks_completed += batch_tasks_completed.sum().item()
        total_tasks_available += batch_tasks_available * batch_size

        # 计算效率分数（任务完成率 * 收益率）
        completion_rate = batch_tasks_completed / batch_tasks_available
        efficiency_score = completion_rate * revenue_rate
        total_efficiency_score += efficiency_score.sum().item()

        # 收集方差数据
        reward_variance.extend(reward.cpu().numpy().tolist())
        revenue_rate_variance.extend(revenue_rate.cpu().numpy().tolist())

        # 计算负载均衡指标
        for b in range(batch_size):
            batch_satellite_counts = torch.zeros(num_satellites)
            for i in range(len(satellite_indices[b])):
                if i > 0:  # 跳过起始点
                    sat_idx = satellite_indices[b][i].item()
                    batch_satellite_counts[sat_idx] += 1
                    satellite_task_distribution[sat_idx] += 1

            # 计算该批次的负载均衡分数
            if batch_satellite_counts.sum() > 0:
                std_dev = torch.std(batch_satellite_counts.float())
                mean_tasks = torch.mean(batch_satellite_counts.float())
                balance_score = 1.0 - (std_dev / (mean_tasks + 1e-8))
                load_balance_scores.append(balance_score.item())

        # 计算平均值
        reward_mean = reward.mean().item()
        revenue_rate_mean = revenue_rate.mean().item()
        distance_mean = distance.mean().item()
        memory_mean = memory.mean().item()
        power_mean = power.mean().item()
        efficiency_mean = efficiency_score.mean().item()
        
        # 每10个batch打印一次结果
        if (batch_idx + 1) % 10 == 0 or batch_idx == len(data_loader) - 1:
            log_message('Test Batch %d/%d, reward: %2.3f, revenue_rate: %2.4f, efficiency: %2.4f, distance: %2.4f, memory: %2.4f, power: %2.4f' %
                  (batch_idx + 1, len(data_loader), reward_mean, revenue_rate_mean, efficiency_mean, distance_mean, memory_mean, power_mean),
                  to_console=verbose)
        
        if render_fn is not None and batch_idx < num_plot:
            name = 'test_batch%d_%2.4f.png' % (batch_idx, revenue_rate_mean)
            path = os.path.join(save_dir, name)
            render_fn(static, tour_indices, satellite_indices, path, num_satellites, 0)
    
    # 计算平均指标
    avg_reward = total_reward / total_samples if total_samples > 0 else 0
    avg_revenue_rate = total_revenue_rate / total_samples if total_samples > 0 else 0
    avg_distance = total_distance / total_samples if total_samples > 0 else 0
    avg_memory = total_memory / total_samples if total_samples > 0 else 0
    avg_power = total_power / total_samples if total_samples > 0 else 0
    avg_efficiency = total_efficiency_score / total_samples if total_samples > 0 else 0

    # 计算任务完成率
    task_completion_rate = total_tasks_completed / total_tasks_available if total_tasks_available > 0 else 0

    # 计算方差和标准差
    import numpy as np
    reward_std = np.std(reward_variance) if len(reward_variance) > 0 else 0
    revenue_rate_std = np.std(revenue_rate_variance) if len(revenue_rate_variance) > 0 else 0

    # 计算负载均衡统计
    avg_load_balance = np.mean(load_balance_scores) if load_balance_scores else 0.0
    load_balance_std = np.std(load_balance_scores) if load_balance_scores else 0.0

    # 计算每颗卫星的任务分配比例
    total_distributed_tasks = satellite_task_distribution.sum()
    satellite_distribution_ratio = satellite_task_distribution / (total_distributed_tasks + 1e-8)

    log_message('Test Summary - Avg reward: %2.3f±%2.3f, revenue_rate: %2.4f±%2.4f, efficiency: %2.4f, completion_rate: %2.4f, distance: %2.4f, memory: %2.4f, power: %2.4f' %
          (avg_reward, reward_std, avg_revenue_rate, revenue_rate_std, avg_efficiency, task_completion_rate, avg_distance, avg_memory, avg_power))

    # 输出负载均衡信息
    log_message('Load Balance - Avg balance score: %2.4f±%2.4f' % (avg_load_balance, load_balance_std))
    log_message('Task Distribution by Satellite:')
    for sat_idx in range(num_satellites):
        log_message('  Satellite %d: %d tasks (%.2f%%)' %
                   (sat_idx + 1, int(satellite_task_distribution[sat_idx]),
                    satellite_distribution_ratio[sat_idx] * 100))

    actor.train()
    return avg_reward, avg_revenue_rate, avg_distance, avg_memory, avg_power


def train_single_constellation_mode(constellation_mode):
    """
    训练单个星座模式的函数
    """
    global log_file

    log_message(f"\n{'='*60}")
    log_message(f"开始训练星座模式: {constellation_mode.upper()}")
    log_message(f"{'='*60}")

    # 创建数据集
    train_data = ConstellationSMPDataset(
        args.num_nodes,
        args.train_size,
        args.seed,
        args.memory_total,
        args.power_total,
        args.num_satellites
    )

    valid_data = ConstellationSMPDataset(
        args.num_nodes,
        args.valid_size,
        args.seed + 1,
        args.memory_total,
        args.power_total,
        args.num_satellites
    )

    # 创建保存目录（包含星座模式信息）
    now = '%s' % datetime.datetime.now().strftime('%Y_%m_%d_%H_%M_%S')
    save_dir = os.path.join(args.task, args.task + '%d' % args.num_nodes,
                           f'constellation_{args.model}{args.rnn}_{constellation_mode}_{now}')
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    # 创建模式专用日志文件（不影响主日志文件）
    mode_log_file = None
    f_dir = os.path.join(save_dir, 'log.txt')
    mode_log_file = open(f_dir, 'a+', encoding='utf-8')

    # 保存训练信息
    log_message(args.task + ': ' + str(args.num_nodes))
    log_message('model: ' + str(args.model))
    log_message('rnn: ' + str(args.rnn))
    log_message('hidden_size: ' + str(args.hidden_size))
    log_message('batch_size: ' + str(args.batch_size))
    log_message('seed: ' + str(args.seed))
    log_message('train-size: ' + str(args.train_size))
    log_message('valid-size: ' + str(args.valid_size))
    log_message('epochs: ' + str(args.epochs))
    log_message('lr: ' + str(args.lr))
    log_message('memory_total: ' + str(args.memory_total))
    log_message('power_total: ' + str(args.power_total))
    log_message('dropout: ' + str(args.dropout))
    log_message('actor_lr: ' + str(args.actor_lr))
    log_message('critic_lr: ' + str(args.critic_lr))
    log_message('num_satellites: ' + str(args.num_satellites))
    log_message('constellation_mode: ' + str(constellation_mode))  # 使用传入的模式
    log_message('verbose: ' + str(args.verbose))
    log_message(now)

    # 创建配置管理器
    config_manager = ConfigManager()
    config_manager.update_from_args(args)

    # 验证配置
    is_valid, errors = config_manager.validate_config()
    if not is_valid:
        log_message(f"配置验证失败: {'; '.join(errors)}")
        return

    # 创建模型管理器
    model_manager = ModelManager(args.model, args, train_data)
    actor = model_manager.actor
    critic = model_manager.critic

    # 打印模型摘要
    model_summary = model_manager.get_model_summary()
    log_message(f"使用模型: {model_summary['model_type']}")
    log_message(f"Actor参数数量: {model_summary.get('actor_parameters', 0):,}")
    log_message(f"Critic参数数量: {model_summary['critic_parameters']:,}")

    # 转换参数为字典
    kwargs = vars(args)
    kwargs['train_data'] = train_data
    kwargs['valid_data'] = valid_data
    kwargs['reward_fn'] = reward
    kwargs['render_fn'] = render
    kwargs['save_dir'] = save_dir

    # 加载检查点（如果有）
    if args.checkpoint:
        path = os.path.join(args.checkpoint, 'actor.pt')
        actor.load_state_dict(torch.load(path, device))
        path = os.path.join(args.checkpoint, 'critic.pt')
        critic.load_state_dict(torch.load(path, device))

    # 训练或测试
    if not args.test:
        # 更新kwargs中的constellation_mode
        kwargs['constellation_mode'] = constellation_mode
        train_constellation_smp_process(actor, critic, **kwargs)

    # 测试模型
    test_data = ConstellationSMPDataset(
        args.num_nodes,
        args.valid_size,
        args.seed + 2,
        args.memory_total,
        args.power_total,
        args.num_satellites
    )

    test_dir = os.path.join(save_dir, 'test_constellation')
    test_loader = DataLoader(test_data, args.batch_size, False, num_workers=0)
    log_message("\n开始测试模型...")
    avg_reward, revenue_rate_avg, _, _, _ = validate_constellation_smp(
        test_loader, actor, reward, args.num_satellites, render, test_dir,
        num_plot=5, verbose=args.verbose, constellation_mode=constellation_mode
    )
    log_message('测试完成 - 平均奖励: %2.3f, 平均星座收益率: %2.4f' % (avg_reward, revenue_rate_avg))

    # 关闭模式专用日志文件（不关闭主日志文件）
    if mode_log_file is not None:
        mode_log_file.close()

    return save_dir, avg_reward, revenue_rate_avg


def get_training_modes():
    """
    根据用户配置确定要训练的星座模式
    """
    if args.training_mode == 'single':
        # 单一模式：使用constellation_mode参数指定的模式
        return [args.constellation_mode]
    elif args.training_mode == 'all':
        # 所有模式：训练三种模式
        return ['cooperative', 'competitive', 'hybrid']
    elif args.training_mode == 'compare':
        # 对比模式：使用modes_to_train参数指定的模式列表
        if args.modes_to_train:
            return args.modes_to_train
        else:
            # 如果没有指定，默认训练所有模式
            return ['cooperative', 'competitive', 'hybrid']
    else:
        # 默认情况
        return [args.constellation_mode]


def train_constellation_smp():
    """
    训练星座任务规划模型的主函数
    """
    global log_file
    constellation_modes = get_training_modes()
    results = {}

    # 创建主日志文件
    now = '%s' % datetime.datetime.now().strftime('%Y_%m_%d_%H_%M_%S')
    main_log_dir = os.path.join(args.task, args.task + '%d' % args.num_nodes, f'multi_mode_training_{now}')
    if not os.path.exists(main_log_dir):
        os.makedirs(main_log_dir)

    main_log_file = os.path.join(main_log_dir, 'training_summary.txt')
    log_file = open(main_log_file, 'w', encoding='utf-8')

    # 根据训练模式显示不同的信息
    if len(constellation_modes) == 1:
        log_message(f"🛰️  开始单模式星座任务规划训练")
        log_message(f"训练模式: {constellation_modes[0].upper()}")
    else:
        log_message(f"🛰️  开始多模式星座任务规划训练")
        log_message(f"将依次训练以下模式: {', '.join([m.upper() for m in constellation_modes])}")

    log_message(f"训练配置: {args.training_mode}")
    log_message(f"主日志目录: {main_log_dir}")
    log_message("=" * 80)

    for i, mode in enumerate(constellation_modes, 1):
        log_message(f"\n🚀 [{i}/{len(constellation_modes)}] 开始训练模式: {mode.upper()}")

        try:
            save_dir, avg_reward, revenue_rate = train_single_constellation_mode(mode)
            results[mode] = {
                'save_dir': save_dir,
                'avg_reward': avg_reward,
                'revenue_rate': revenue_rate,
                'status': 'success'
            }
            log_message(f"✅ 模式 {mode.upper()} 训练完成")
            log_message(f"   保存路径: {save_dir}")
            log_message(f"   平均奖励: {avg_reward:.4f}")
            log_message(f"   收益率: {revenue_rate:.4f}")

        except Exception as e:
            log_message(f"❌ 模式 {mode.upper()} 训练失败: {str(e)}")
            results[mode] = {
                'status': 'failed',
                'error': str(e)
            }
            import traceback
            log_message(f"错误详情:\n{traceback.format_exc()}")

    # 输出最终总结
    log_message("\n" + "=" * 80)
    if len(constellation_modes) == 1:
        log_message("🎯 单模式训练总结")
    else:
        log_message("🎯 多模式训练总结")
    log_message("=" * 80)

    for mode, result in results.items():
        if result['status'] == 'success':
            log_message(f"✅ {mode.upper()}: 奖励={result['avg_reward']:.4f}, 收益率={result['revenue_rate']:.4f}")
        else:
            log_message(f"❌ {mode.upper()}: 训练失败")

    # 找出最佳模式（仅在多模式时进行比较）
    successful_results = {k: v for k, v in results.items() if v['status'] == 'success'}
    if successful_results:
        if len(successful_results) > 1:
            best_mode = max(successful_results.keys(),
                           key=lambda x: successful_results[x]['avg_reward'])
            best_result = successful_results[best_mode]
            log_message(f"\n🏆 最佳模式: {best_mode.upper()}")
            log_message(f"   最高奖励: {best_result['avg_reward']:.4f}")
            log_message(f"   对应收益率: {best_result['revenue_rate']:.4f}")
            log_message(f"   模型路径: {best_result['save_dir']}")
        else:
            # 单模式情况
            mode, result = list(successful_results.items())[0]
            log_message(f"\n✅ 训练完成: {mode.upper()}")
            log_message(f"   最终奖励: {result['avg_reward']:.4f}")
            log_message(f"   最终收益率: {result['revenue_rate']:.4f}")
            log_message(f"   模型路径: {result['save_dir']}")

    if len(constellation_modes) == 1:
        log_message("\n🎉 单模式训练完成！")
    else:
        log_message("\n🎉 所有模式训练完成！")

    # 关闭主日志文件
    if log_file is not None:
        log_file.close()
        log_file = None

    return results


def train_constellation_smp_process(actor, critic, train_data, valid_data, reward_fn,
                                   render_fn, batch_size, actor_lr, critic_lr, max_grad_norm,
                                   epochs, num_satellites, save_dir, weight_decay, verbose=False, constellation_mode='cooperative', **kwargs):
    """
    星座任务规划模型的训练过程
    """
    # 定义优化器，并加入权重衰减
    actor_optim = optim.Adam(actor.parameters(), lr=actor_lr, weight_decay=weight_decay)
    critic_optim = optim.Adam(critic.parameters(), lr=critic_lr, weight_decay=weight_decay)
    
    # 使用更稳定的学习率调度器
    actor_scheduler = optim.lr_scheduler.StepLR(
        actor_optim,
        step_size=5,  # 每5个epoch降低学习率
        gamma=0.8,    # 学习率衰减因子
    )
    critic_scheduler = optim.lr_scheduler.StepLR(
        critic_optim,
        step_size=5,  # 每5个epoch降低学习率
        gamma=0.8,    # 学习率衰减因子
    )

    # 定义数据加载器
    num_workers = os.cpu_count() // 2  # 使用一半的CPU核心
    train_loader = DataLoader(train_data, batch_size, True, num_workers=num_workers)
    valid_loader = DataLoader(valid_data, batch_size, False, num_workers=num_workers)
    best_params = None
    best_reward = -np.inf  # 初始化为负无穷，因为我们要最大化奖励
    times, losses, rewards, critic_rewards, revenue_rates, distances, memories, powers = [], [], [], [], [], [], [], []

    # 开始训练
    for epoch in range(epochs):
        # 创建每个epoch的保存目录
        epoch_dir = os.path.join(save_dir, 'epoch%s' % epoch)
        if not os.path.exists(epoch_dir):
            os.makedirs(epoch_dir)

        # 开始训练
        actor.train()
        critic.train()
        epoch_start = time.time()
        start = epoch_start
        
        log_message(f"\n开始训练 Epoch {epoch+1}/{epochs}")
        
        for batch_idx, batch in enumerate(train_loader):
            static, dynamic, x0 = batch
            static = static.to(device)
            dynamic = dynamic.to(device)
            
            # 前向传播
            tour_indices, satellite_indices, tour_log_prob = actor(static, dynamic)
            
            # 计算奖励
            reward, revenue_rate, distance, memory, power = reward_fn(static, tour_indices, satellite_indices, constellation_mode)

            # 计算负载均衡指标
            actual_batch_size = satellite_indices.size(0)  # 使用实际的批次大小
            batch_satellite_counts = torch.zeros(actual_batch_size, num_satellites, device=static.device)
            for b in range(actual_batch_size):
                # 获取当前批次的卫星索引序列
                sat_seq = satellite_indices[b]
                seq_len = sat_seq.size(0) if hasattr(sat_seq, 'size') else len(sat_seq)

                for i in range(seq_len):
                    if i > 0:  # 跳过起始点
                        sat_idx = sat_seq[i].item() if hasattr(sat_seq[i], 'item') else int(sat_seq[i])
                        if 0 <= sat_idx < num_satellites:  # 确保索引有效
                            batch_satellite_counts[b, sat_idx] += 1

            # 计算负载均衡分数
            load_balance_scores = []
            for b in range(actual_batch_size):
                sat_counts = batch_satellite_counts[b]
                if sat_counts.sum() > 0:
                    std_dev = torch.std(sat_counts.float())
                    mean_tasks = torch.mean(sat_counts.float())
                    balance_score = 1.0 - torch.clamp(std_dev / (mean_tasks + 1e-8), 0.0, 1.0)
                    load_balance_scores.append(balance_score.item())
                else:
                    load_balance_scores.append(0.0)

            avg_load_balance = np.mean(load_balance_scores)

            # 评估基线
            critic_est = critic(static, dynamic).view(-1)
            advantage = (reward - critic_est)

            # 计算critic损失
            critic_loss = torch.mean(advantage ** 2)

            # 动态梯度归一化 - 根据任务规模调整阈值
            # 100节点任务使用50.0阈值，200节点任务使用100.0阈值
            dynamic_threshold = max(20.0, min(100.0, kwargs.get('num_nodes', 100) * 0.5))

            # 渐进式归一化，保留更多原始信号
            if advantage.std() > dynamic_threshold:
                # 保留70%原始信号 + 30%归一化信号
                normalized_adv = advantage / (advantage.std() + 1e-8) * (dynamic_threshold * 0.5)
                advantage = 0.7 * advantage + 0.3 * normalized_adv

            # 计算actor损失（策略梯度：最大化优势函数）
            actor_loss = torch.mean(-advantage.detach() * tour_log_prob.sum(dim=1))
            
            # 优化器步骤
            actor_optim.zero_grad()
            actor_loss.backward()

            # 计算梯度范数用于监控
            actor_grad_norm = torch.nn.utils.clip_grad_norm_(actor.parameters(), max_grad_norm)
            actor_optim.step()

            critic_optim.zero_grad()
            critic_loss.backward()
            critic_grad_norm = torch.nn.utils.clip_grad_norm_(critic.parameters(), max_grad_norm)
            critic_optim.step()
            
            # 记录指标
            losses.append(critic_loss.item())
            rewards.append(reward.mean().item())
            critic_rewards.append(critic_est.mean().item())
            revenue_rates.append(revenue_rate.mean().item())
            distances.append(distance.mean().item())
            memories.append(memory.mean().item())
            powers.append(power.mean().item())

            # 记录梯度范数和负载均衡用于监控训练稳定性
            if batch_idx % 50 == 0 and verbose:
                # 计算当前批次的卫星任务分配统计
                sat_task_stats = []
                total_tasks = batch_satellite_counts.sum().item()

                for sat_idx in range(num_satellites):
                    sat_tasks = batch_satellite_counts[:, sat_idx].sum().item()
                    percentage = (sat_tasks / max(total_tasks, 1)) * 100
                    sat_task_stats.append(f"S{sat_idx}:{int(sat_tasks)}({percentage:.1f}%)")

                # 添加优势函数诊断信息
                advantage_mean = advantage.mean().item()
                advantage_std = advantage.std().item()
                advantage_range = f"[{advantage.min().item():.2f}, {advantage.max().item():.2f}]"

                log_message(f"Batch {batch_idx}: "
                           f"Reward: {reward.mean().item():.4f}, "
                           f"Loss: {critic_loss.item():.4f}, "
                           f"Revenue: {revenue_rate.mean().item():.4f}, "
                           f"LoadBalance: {avg_load_balance:.4f}, "
                           f"Tasks: [{', '.join(sat_task_stats)}], "
                           f"ActorGrad: {actor_grad_norm:.4f}, "
                           f"CriticGrad: {critic_grad_norm:.4f}, "
                           f"Advantage: μ={advantage_mean:.3f}, σ={advantage_std:.3f}, range={advantage_range}")
            
            # 打印训练进度
            if (batch_idx + 1) % 50 == 0:
                end = time.time()
                times.append(end - start)
                start = end

                # 计算最近50个batch的负载均衡统计
                recent_rewards = rewards[-50:]
                recent_revenue_rates = revenue_rates[-50:]
                recent_losses = losses[-50:]

                # 计算奖励和损失的变化趋势
                reward_trend = "↑" if len(recent_rewards) >= 2 and recent_rewards[-1] > recent_rewards[0] else "↓"
                loss_trend = "↓" if len(recent_losses) >= 2 and recent_losses[-1] < recent_losses[0] else "↑"

                log_message('Epoch %d, Batch %d/%d, loss: %2.3f%s, reward: %2.3f%s, critic_reward: %2.3f, revenue_rate: %2.4f, distance: %2.4f, memory: %2.4f, power: %2.4f, lr: %.6f, took: %.3fs' %
                      (epoch + 1, batch_idx + 1, len(train_loader),
                       np.mean(recent_losses), loss_trend,
                       np.mean(recent_rewards), reward_trend,
                       np.mean(critic_rewards[-50:]),
                       np.mean(recent_revenue_rates),
                       np.mean(distances[-50:]),
                       np.mean(memories[-50:]),
                       np.mean(powers[-50:]),
                       actor_optim.param_groups[0]['lr'],
                       times[-1]),
                      to_console=True)
        
        # Epoch结束统计
        epoch_avg_reward = np.mean(rewards[-len(train_loader):])
        epoch_avg_loss = np.mean(losses[-len(train_loader):])
        epoch_avg_revenue = np.mean(revenue_rates[-len(train_loader):])

        log_message(f"\n📊 Epoch {epoch + 1} 训练统计:")
        log_message(f"  平均奖励: {epoch_avg_reward:.4f}")
        log_message(f"  平均损失: {epoch_avg_loss:.4f}")
        log_message(f"  平均收益率: {epoch_avg_revenue:.4f}")
        log_message(f"  当前学习率: {actor_optim.param_groups[0]['lr']:.6f}")

        # 每个epoch结束后进行验证
        log_message("\n🔍 开始验证...")
        valid_reward, valid_revenue_rate, valid_distance, valid_memory, valid_power = validate_constellation_smp(valid_loader, actor, reward_fn, num_satellites, render_fn, epoch_dir, num_plot=5, verbose=True, constellation_mode=constellation_mode)
        log_message('✅ 验证完成 - Epoch %d, reward: %2.3f, revenue_rate: %2.4f, distance: %2.4f, memory: %2.4f, power: %2.4f' %
              (epoch + 1, valid_reward, valid_revenue_rate, valid_distance, valid_memory, valid_power))

        # 计算训练验证差距
        train_valid_gap = epoch_avg_reward - valid_reward
        if abs(train_valid_gap) > 2.0:
            gap_status = "⚠️ 过拟合" if train_valid_gap > 0 else "⚠️ 欠拟合"
            log_message(f"  {gap_status}: 训练验证差距 = {train_valid_gap:.4f}")
        else:
            log_message(f"  ✅ 训练验证差距正常: {train_valid_gap:.4f}")
        
        # 更新学习率
        actor_scheduler.step()
        critic_scheduler.step()
        
        # 保存最佳模型
        if valid_reward > best_reward:
            best_reward = valid_reward
            actor_save_path = os.path.join(save_dir, 'actor.pt')
            critic_save_path = os.path.join(save_dir, 'critic.pt')
            torch.save(actor.state_dict(), actor_save_path)
            torch.save(critic.state_dict(), critic_save_path)
            log_message(f"已保存新模型到 {save_dir} (验证集奖励: {best_reward:.4f})")
        
        # 绘制训练曲线
        plot_single_smp_train_loss(
            save_dir, 
            np.array(times).cumsum(), 
            losses, rewards, critic_rewards, 
            revenue_rates, distances, memories, powers
        )
        
    log_message("训练完成")


if __name__ == '__main__':
    train_constellation_smp() 